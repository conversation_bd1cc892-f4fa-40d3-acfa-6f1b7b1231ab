package com.ruoyi.web.controller.antiblock;

import com.ruoyi.common.constant.HttpStatus;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.ip.IpUtils;
import com.ruoyi.system.config.AntiBlockConfig;
import com.ruoyi.system.service.antiblock.AntiBlockService;
import com.ruoyi.system.service.antiblock.AntiBlockBizHandlerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.TimeUnit;

/**
 * 防风控跳链控制器
 * 通过多域名跳转规避微信等平台的风控系统对链接的识别与封禁
 * 
 * 跳转链路：
 * 1. 用户访问 a.com/entry -> 302跳转到 b.com/step1
 * 2. 用户访问 b.com/step1 -> 307跳转到 b.com/step2  
 * 3. 用户访问 b.com/step2 -> 302跳转到最终微信文章链接
 *
 * <AUTHOR>
 * @date 2025/07/29
 */
@Slf4j
@Controller
@RequestMapping("/antiblock")
public class AntiBlockController {


    @Autowired
    private AntiBlockService antiBlockService;

    @Autowired
    private AntiBlockConfig antiBlockConfig;

    @Autowired
    private AntiBlockBizHandlerService bizHandlerService;

    /**
     * 业务入口接口 - 第一步跳转
     * 根据业务代码决定最终跳转的目标链接
     *
     * @param bizCode 业务代码（如：weixin、douyin、qzone等）
     * @param targetCode 目标代码（可选，业务可根据此参数选择具体文章）
     * @param request HTTP请求对象
     * @param response HTTP响应对象
     * @throws IOException IO异常
     */
    @CrossOrigin
    @GetMapping({"/entry/{bizCode}", "/entry/{bizCode}/{targetCode}"})
    public void entry(@PathVariable("bizCode") String bizCode,
                     @PathVariable(value = "targetCode", required = false) String targetCode,
                     HttpServletRequest request,
                     HttpServletResponse response) throws IOException {
        
        String clientIp = IpUtils.getIpAddr(request);
        String userAgent = request.getHeader("User-Agent");

        log.info("防风控跳链-业务入口访问, bizCode={}, targetCode={}, userAgent={}, ip={}",
                bizCode, targetCode, userAgent, clientIp);

        // 检查功能是否启用
        if (!antiBlockConfig.isEnabled()) {
            log.warn("防风控跳链功能已禁用");
            response.setStatus(HttpStatus.SERVICE_UNAVAILABLE);
            return;
        }

        // 验证业务代码
        if (!bizHandlerService.isValidBizCode(bizCode)) {
            log.warn("防风控跳链-无效的业务代码: {}", bizCode);
            response.setStatus(HttpStatus.NOT_FOUND);
            return;
        }

        // 获取目标URL（由业务处理器自己决定）
        String targetUrl = bizHandlerService.getTargetUrl(bizCode, targetCode, request);
        if (StringUtils.isBlank(targetUrl)) {
            log.warn("防风控跳链-无法获取目标URL: bizCode={}, targetCode={}", bizCode, targetCode);
            response.setStatus(HttpStatus.BAD_REQUEST);
            return;
        }


        String stepDomain = request.getServerName();

        // 判断是否启用加密（根据业务处理器配置）
        boolean encrypt = bizHandlerService.isEncryptEnabled(bizCode);
        
        try {
            String processedTargetUrl;

            if (encrypt) {
                // 加密目标URL
                String encryptedTargetUrl = antiBlockService.encryptTargetUrl(targetUrl);
                processedTargetUrl = StringUtils.isNotBlank(encryptedTargetUrl) ? encryptedTargetUrl : targetUrl;
            } else {
                // 不加密，直接使用原URL
                processedTargetUrl = targetUrl;
            }

            // URL编码处理后的字符串
            String encodedTargetUrl = URLEncoder.encode(processedTargetUrl, StandardCharsets.UTF_8.toString());

            // 构建step1的跳转链接（使用HTTPS协议）
            String step1Url = String.format("http://%s:8778/antiblock/step1?targetUrl=%s",
                    stepDomain, encodedTargetUrl);
            
            log.info("防风控跳链-入口跳转, step1Url={}", step1Url);
            
            // 设置302重定向
            response.setStatus(HttpStatus.FOUND);
            response.setHeader("Location", step1Url);

        } catch (Exception e) {
            log.error("防风控跳链-入口跳转异常, bizCode={}, targetUrl={}", bizCode, targetUrl, e);
            response.setStatus(HttpStatus.ERROR);
        }
    }

    /**
     * 直接URL入口接口 - 第一步跳转
     * 直接从参数接收目标URL进行跳转（targetUrl已经是处理过的，无需再次加密）
     *
     * @param targetUrl 目标URL（必填，已经是加密过或未加密的最终形式）
     * @param request HTTP请求对象
     * @param response HTTP响应对象
     * @throws IOException IO异常
     */
    @CrossOrigin
    @GetMapping("/entry")
    public void entryWithUrl( String targetUrl,
                            HttpServletRequest request,
                            HttpServletResponse response) throws IOException {

        String clientIp = IpUtils.getIpAddr(request);
        String userAgent = request.getHeader("User-Agent");

        log.info("防风控跳链-直接URL入口访问, targetUrl={}, userAgent={}, ip={}",
                targetUrl, userAgent, clientIp);

        // 检查功能是否启用
        if (!antiBlockConfig.isEnabled()) {
            log.warn("防风控跳链功能已禁用");
            response.setStatus(HttpStatus.SERVICE_UNAVAILABLE);
            return;
        }

        // 参数校验
        if (StringUtils.isBlank(targetUrl)) {
            log.warn("防风控跳链-直接URL入口访问失败, targetUrl为空");
            response.setStatus(HttpStatus.BAD_REQUEST);
            return;
        }


        // 智能选择最佳跳转域名
        String stepDomain = request.getServerName();

        try {
            // 直接使用传入的targetUrl（已经是处理过的最终形式）
            log.debug("直接使用传入的targetUrl: {}", targetUrl);

            // URL编码处理后的字符串
            String encodedTargetUrl = URLEncoder.encode(targetUrl, StandardCharsets.UTF_8.toString());

            // 构建step1的跳转链接（使用HTTPS协议）
            String step1Url = String.format("http://%s:8778/antiblock/step1?targetUrl=%s",
                    stepDomain, encodedTargetUrl);

            log.info("防风控跳链-直接URL入口跳转, step1Url={}", step1Url);

            // 设置302重定向
            response.setStatus(HttpStatus.FOUND);
            response.setHeader("Location", step1Url);

        } catch (Exception e) {
            log.error("防风控跳链-直接URL入口跳转异常, targetUrl={}", targetUrl, e);
            response.setStatus(HttpStatus.ERROR);
        }
    }



    /**
     * 第一步接口 - HTTPS到HTTP协议切换
     * 用户访问 b.com/step1（HTTPS），返回307临时重定向到 b.com/step2（HTTP）
     * 使用307状态码实现协议切换，扰乱平台的中间链路跟踪逻辑
     * 
     * @param targetUrl 最终要跳转的目标链接
     * @param request HTTP请求对象
     * @param response HTTP响应对象
     * @throws IOException IO异常
     */
    @CrossOrigin
    @GetMapping("/step1")
    public void step1( String targetUrl,
                     HttpServletRequest request,
                     HttpServletResponse response) throws IOException {
        
        String clientIp = IpUtils.getIpAddr(request);
        String userAgent = request.getHeader("User-Agent");

        log.info("防风控跳链-第一步访问, targetUrl={}, protocol={}, userAgent={}, ip={}",
                targetUrl, request.getScheme(), userAgent, clientIp);


        
        // 参数校验
        if (StringUtils.isBlank(targetUrl)) {
            log.warn("防风控跳链-第一步访问失败, targetUrl为空");
            response.setStatus(HttpStatus.BAD_REQUEST);
            return;
        }
        
        try {
            // 构建step2的跳转链接（切换到HTTP协议）
            // 需要对targetUrl进行URL编码，因为要作为查询参数传递
            String encodedTargetUrl = URLEncoder.encode(targetUrl, StandardCharsets.UTF_8.toString());

            String step2Url = "http://" + request.getServerName() +
                    ":8778/antiblock/step2?targetUrl=" + encodedTargetUrl;
            log.info("防风控跳链-第一步跳转, step2Url={}", step2Url);
            
            // 设置307临时重定向（保持请求方法和请求体）
            response.setStatus(HttpStatus.TEMPORARY_REDIRECT);
            response.setHeader("Location", step2Url);

        } catch (Exception e) {
            log.error("防风控跳链-第一步跳转异常, targetUrl={}", targetUrl, e);
            response.setStatus(HttpStatus.ERROR);
        }
    }

    /**
     * 第二步接口 - 最终跳转
     * 用户访问 b.com/step2（HTTP），返回302跳转到最终的微信文章链接
     * 
     * @param targetUrl 最终要跳转的目标链接
     * @param request HTTP请求对象
     * @param response HTTP响应对象
     * @throws IOException IO异常
     */
    @CrossOrigin
    @GetMapping("/step2")
    public void step2(String targetUrl,
                     HttpServletRequest request,
                     HttpServletResponse response) throws IOException {
        
        String clientIp = IpUtils.getIpAddr(request);
        String userAgent = request.getHeader("User-Agent");

        log.info("防风控跳链-第二步访问, targetUrl={}, protocol={}, userAgent={}, ip={}",
                targetUrl, request.getScheme(), userAgent, clientIp);
        
        // 参数校验
        if (StringUtils.isBlank(targetUrl)) {
            log.warn("防风控跳链-第二步访问失败, targetUrl为空");
            response.setStatus(HttpStatus.BAD_REQUEST);
            return;
        }
        
        try {
            // Spring已经自动进行URL解码，直接使用targetUrl
            String originalTargetUrl;
            if (targetUrl.startsWith(AntiBlockService.ENCRYPTION_PREFIX)) {
                String decryptedUrl = antiBlockService.decryptTargetUrl(targetUrl);
                if (StringUtils.isBlank(decryptedUrl)) {
                    log.warn("防风控跳链-第二步访问失败, URL解密失败: {}", targetUrl);
                    return;
                }
                originalTargetUrl = decryptedUrl;
            } else {
                originalTargetUrl = targetUrl;
            }

            // 验证目标链接格式
            if (!isValidUrl(originalTargetUrl)) {
                log.warn("防风控跳链-第二步访问失败, 无效的目标链接: {}", originalTargetUrl);
                response.setStatus(HttpStatus.BAD_REQUEST);
                return;
            }

            log.info("防风控跳链-最终跳转, finalUrl={}", originalTargetUrl);

            // 设置302重定向到最终目标链接
            response.setStatus(HttpStatus.FOUND);
            response.setHeader("Location", originalTargetUrl);

        } catch (Exception e) {
            log.error("防风控跳链-第二步跳转异常, targetUrl={}", targetUrl, e);
            response.setStatus(HttpStatus.ERROR);
        }
    }



    /**
     * 验证URL格式是否有效
     * 
     * @param url 待验证的URL
     * @return 是否有效
     */
    private boolean isValidUrl(String url) {
        if (StringUtils.isBlank(url)) {
            return false;
        }
        
        // 基本的URL格式验证
        return url.startsWith("http://") || url.startsWith("https://");
    }


}
