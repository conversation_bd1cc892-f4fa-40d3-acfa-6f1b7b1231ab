# 项目相关配置
ruoyi:
  # 名称
  name: RuoYi
  # 版本
  version: 3.6.0
  # 版权年份
  copyrightYear: 2021
  # 实例演示开关
  demoEnabled: true
  # 文件路径 示例（ Windows配置D:/ruoyi/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: ${user.home}/uploadPath
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数组计算 char 字符验证
  captchaType: math

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8778
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # tomcat最大线程数，默认为200
    max-threads: 800
    # Tomcat启动初始化的线程数，默认值25
    min-spare-threads: 30

# 日志配置
logging:
  level:
    com.ruoyi: debug
    org.springframework: warn

# Spring配置
spring:
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    include: druid-dev,antiblick-dev
  # 文件上传
  servlet:
     multipart:
       # 单个文件大小
       max-file-size:  50MB
       # 设置总上传的文件大小
       max-request-size:  100MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  # redis 配置
  redis:
    # 地址
    host: localhost
    # 端口，默认为6379
    port: 6379
    # 数据库索引
    database: 0
    # 密码
    password:
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms

# token配置
token:
    # 令牌自定义标识
    header: Authorization
    # 令牌密钥
    secret: abcdefghijklmnopqrstuvwxyz
    # 令牌有效期（默认30分钟）
    expireTime: 720

# MyBatis配置
mybatis:
    # 搜索指定包别名
    typeAliasesPackage: com.ruoyi.**.domain
    # 配置mapper的扫描，找到所有的mapper.xml映射文件
    mapperLocations: classpath*:mapper/**/*Mapper.xml
    # 加载全局的配置文件
    configLocation: classpath:mybatis/mybatis-config.xml

# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  reasonable: true
  supportMethodsArguments: true
  params: count=countSql

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice/*
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

# 消息队列
rocketmq:
  accessKey: LTAI5tLGKudCmLbqTnSgQCKz
  secretKey: ******************************
  nameSrvAddr: http://MQ_INST_1724318912582463_BXq2xJ6r.mq-internet-access.mq-internet.aliyuncs.com:80
  refresh:
    topic: REFRESH
    groupId: GID_REFRESH
    messageModel: BROADCASTING
  billing:
    topic: BILLING
    groupId: GID_BILLING
    messageModel: CLUSTERING
  log:
    topic: LOG
    groupId: GID_LOG
    messageModel: CLUSTERING

# 分表
tb:
  sharding:
    count:
      consumer: 8
      order: 8

# 聚合接口
juhe:
  key:
    # 身份证实名认证
    idCardAuth: 123456
    # IP解析
    ipAnalysis: c636e52f361cbe1d4f37d0bc1535b2d2

# 毫秒科技接口
haomiao:
  # 身份证实名认证
  idCardAuth:
    secretId: 123456
    secretKey: 123456
  # IP解析
  ip:
    secretId: AKIDMYWT8Xa3FHyaaoha7Wgx4pS9pivcvruag8z4
    secretKey: 8lajp3o309luef6g2xZsaFcvwcYk5zb30yET5o3i

# 爱奇艺落地页送审
iqiyi:
  lp:
    uploadUrl: https://cfe.iqiyi.com/adfe/tyche/api/lp/upload
    updateUrl: https://cfe.iqiyi.com/adfe/tyche/api/lp/update
    cb: http://actenginetest.ydns.cn/lp/audit/callback
    biz: NuoHe
    secretKey: f09e324ea0889e2711e3611b40d6b9

# 钉钉机器人
ding:
  webhook:
    dataAlert: https://oapi.dingtalk.com/robot/send?access_token=0db572409f00f191833b22441829ef77455c71bb4d076260c5e72e0a436213c4
    lpNotice: https://oapi.dingtalk.com/robot/send?access_token=0db572409f00f191833b22441829ef77455c71bb4d076260c5e72e0a436213c4
    crmBiz: https://oapi.dingtalk.com/robot/send?access_token=0db572409f00f191833b22441829ef77455c71bb4d076260c5e72e0a436213c4
    systemAlert: https://oapi.dingtalk.com/robot/send?access_token=0db572409f00f191833b22441829ef77455c71bb4d076260c5e72e0a436213c4
    bizAlert: https://oapi.dingtalk.com/robot/send?access_token=0db572409f00f191833b22441829ef77455c71bb4d076260c5e72e0a436213c4
    ocpcAlert: https://oapi.dingtalk.com/robot/send?access_token=0db572409f00f191833b22441829ef77455c71bb4d076260c5e72e0a436213c4
    balanceAlert: https://oapi.dingtalk.com/robot/send?access_token=0db572409f00f191833b22441829ef77455c71bb4d076260c5e72e0a436213c4
    oceanAlert: https://oapi.dingtalk.com/robot/send?access_token=0db572409f00f191833b22441829ef77455c71bb4d076260c5e72e0a436213c4
    domainAlert: https://oapi.dingtalk.com/robot/send?access_token=0db572409f00f191833b22441829ef77455c71bb4d076260c5e72e0a436213c4
    articleAlert: https://oapi.dingtalk.com/robot/send?access_token=0db572409f00f191833b22441829ef77455c71bb4d076260c5e72e0a436213c4
    articleOperateAlert: https://oapi.dingtalk.com/robot/send?access_token=0db572409f00f191833b22441829ef77455c71bb4d076260c5e72e0a436213c4

wx:
  miniapp:
    appid: wxde8329e9b98c240d
    secret: ba668592a2d47c27e4ae9c7a3ed9e92a

alipay:
  appId: xx
  privateKey: xx
  alipayPublicKey: xx
  bizToken: xx
  principalTag: xx

wxpay:
  appId: ww6c363fa128257483
  mchId: 1622001066
  partnerKey: 9PJ4DYCAF2LZIK18BVSHN6ORGXE5U3T0
  notifyUrl: http://apitest.gamingtime.cn/lp/pay/callback
  gzhAppId: wxe9106e6999e3cd1d
  gzhSecret: a3befdce92a1d844129f0cf452323b3c

qwtf:
  # 企微
  server:
    corpid: ww710984161b752a44
    corpsecret: fb1cCPgjICulc-BFhP7Plx7saPqnB_yY38GVs13VPNg
  # 小程序
  client:
    appId: wx5b4a9496d2f404b3
    secret: 06f60c89ecdd54db4e94a6eabedc3ba3

# 灯火上报
dh:
  configs:
    - principalId: 1147711433
      appId: 2021002175686097
      privateKey: MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCCH7F5zvD/U02hR+T9MQ/quyGHqRJL3j/zvpfs7D9vVtBvjuoI8owCY0jR+1+mI2LGfZoMX8ZMU+6jDAIyoegEwRy6sPbPQrz+PIf+KGFFAaI+u29Hlx2GJMRcS9iz+sxkGe1iEJOekknu9wKJ0xieNTMz/z5AdEnYWZ5aXnXJXvPrGwpmPWbdNZhJ9Pfcf6x27nU8rkY1gHdhGN6j7SdxGMgLnmBFwaCobiK3ekJnNEE3CjKLMH2K5dIncN6o7kUW/fWmOuucpryTeBqz+fnaoOK8ErDyoZq3HIGzxhHoG7dd5i0x+ULiiZ/EzLbxrYQm/V9Q0fXnX/d5HiQGQ7vFAgMBAAECggEAAVQLfr/TC/gwua2/M6p1jCLe48ChFC8vnfBbIqsCtsM/G3RtBSxcq4WR77rOLib8UiRkFy4/GvZcYEO2HggzEyFZhrpZuqHmxf2eMcT9wTOUEWneZ++t+HmVm4+WpbewsHDnUHH039CEerEA0KFcBflrDWPUodnOCyxXBnsiXqzanmy/vA9g1wZ1Y4BW/HptktTMNgBGJhib+TGB6F9lxZjdkjBedqov0Ocx15vhGjVBTDmpql0+so9KKGTfTWw9fRhhJeVEsCiTL2Io8fklpMr7tV6D/T6L9YDUl0gvDJ3O3OYFcEfXrTMlKA/RpYpkooA/LA9XUYXbHycTtyMr/QKBgQDqmn9mZVY5Hc4UdzDYlcLv9vmTxjRc4QPkxfTVp3BTJmor3+CCYv0oCJLYaW2Gpvc8Li4xPfjk92zs9UkmSEMcVAxYPwqtCAFr0mz+LPED+96DSl44cp9GH7Bme6s8t9yfwQyjiJKAdEmTD6a71DC2mFjBBsZ5uMx0siuZDknilwKBgQCN/dABECqQs89wtKPKxycYnzFTMKV6N385hYcbBGgcO+WmGWWa6EIuvI1TLmU9DowJcVdDfdlwFrrSLxUWBD1elpsKWyqp8eoQGbBLizV+4rqHGyNOHdfG/OhvXIs1UA/XcB9vhnjCzBH+eFCRXP51M/9vGsf99LpLq7PWRVEMAwKBgQCSAXM2c+z0gRMnc4KH2LTIiWmMSXd/Ml8s+vJEIf/rD+ZVxfBA1YB+cM+LNhn/ClymLIt9A/ep8D7vPjy3QZPaxogkR5/KTkgsJXrFuYyKES/Lf/YhpVxw1PoA3giVMkxxdED8advbbAP6LWx0SzvB+BCMmT3etV8xZU1y5QygowKBgHjWzpR5C6CGRj4mnVZPUohzwmiH9+whGtb7OlXV1DxjBTfsWToR+3EG6kcmLuZdYPQQTzGTAt+PmIKTqyBHj2rWRS/1EkP7KzV0h0JjQK2xiwi8IbmzUBV62t0bJyXB8xlnxHkHOfKd9ZpZDkgHmk5oaDRH2HfaNEl94NizuRu9AoGBAKP2HxA8sLrCAn5/+hQpk678XLM/YulFCdP7qXWztF2ar+WUM7QW9GolpVJ7Kl4AzGHdk7U2bjhq6ModA/fSNGqCoEdVOVZZ54dKFBN9wwkbXMVpLaqKSne6M6mzVzrQ1d+gTrz87k7vFrNGsONuS8p0KAJw4UenXoZrCSVS7GRp
      alipayPublicKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAo35OzbXMkq5Q+juJfc6pUJKrLzNZIM0yzHLqn8VHQc8ojk/D7ljJn8ytD3Rvo1bXdK8btc288oXkrqlUpnO5KO2Tt7lRiB0sitCtZgPYkeDaFTy7AbilWj2TIe5Tb5wCGP053MavxxmwUUSPWzbbW6Fpc0uI5mQdTAlEZE+P9vH0TLmdZ15FaNGK609FlellM9d+Z1pna6eSIhD/uWcLQXN3AxN+ZBcJRbUWgPhAR9HtVrNYmr4FCoLOYz46buwpEyzIWHI36FPSSeAuvcr18myOuP5GprDJ9eeO5xvETd+2HG2sACx/5KCxC9lQ8+b0uik/uvCPCKHqXqZo3n25MwIDAQAB
      bizToken: 19f1503ab1ee4f19bded4b110ed61aaf
      principalTag: 5b3127aa3c6f4c15a877b63056619194
      conversionType: 219
    - principalId: 79000486
      appId: 2021003190644404
      privateKey: MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCVcVP4JC+onn0CUWj+Jz7I6583wcNU5IkYBJmfVTJ0sFZYojr5Zb6Gqv7t4pWMnrBWZ6AKJvjAnQHyzAUJ9T8+AwaK7LYrfe7ypbM5oJwPq5BFBJwJpY2MNWxlJVN7RSwbTRG2xktR7miv8lPePMbDm8mHunr0O2asgp72oavR0OU5ZxzQkdKmi9pcocLenSt4tEIYFNKMSz+3qD6OI1igUXtk+EcQw1DxNAZd9w12gBdkbuO0Zf5AcuiRqqiHpoYSqwx4FRaG9VRyme97OpodPj3muHlWWk7AC3BSrkb86smj+k0wKfqzVGjRaXEPYEdhnB4Jcdxie1buK8zy/AtLAgMBAAECggEAJpPoz0fYHbk8IBsP9XwAEzHwGh6k5AdS9KZLWfxOWB0lTSAkBTmko3ME+Dfjzzcv4j5B7Bz/Xgmp9RmBu35Z4xI44CgXJscNpgnsaFXzs3/gcFCuTzczgRxRbRLjqBDFQA73Ia2yx+W5lKID0Hzz+o7+yVD0aJPk/2eAzb4ScskI5jEHAxtl3wndsYyRmcN65JzlWXr2mh6BZemaLM/yoxzx3qzCJUrHb//nRnVj63o20zgG13k+4fIPTH/94sJRL92E33PgEykdbWgIN2tiMarbSZNJLLARCxSEIdknoUH5BhP753vVMw5QwLuhWZqOHSX9UGzX9bhqgcrBeoqc4QKBgQDy6cMxcWWfxogk9AzfTVf4FO8bwxU32CRAFwtkg4qO9zTzOzvBKQNC/ZxKLBNBgHL3r3r6XDfgbQgcNBtQSgqrr5AB8R+JInodLBDmAs7IMx/VIgz0Or1GEzg51gx8/ZPiWBarlaANDy6a8ZsvYh6VxRrTdaHJGKldbafReo/ekwKBgQCdfm3R8sMbzrzpMsyWPSa+35Twur1yYE4qvynwfaCDZxUh9/F41KPOuBL6xkrmL8MQbqrx6qeenBx8yz79KoJm9XrntZWIcaDDoV1XjxB9fk3JorSeMmIVnE7Kh+5dKcZrsvOr10BoWMSw7SVqHt4heF5j7pntioSlyih55GbbaQKBgQCaQ8JmX/F96oMDlMmn9mJrdj/zjYyoDmfXG8tsZe1UpcHGxlNIdFdR7+WVZTBFZOGeUp7y6CXELPn9j0nZT2w/ZBs8YHj6ih6q8BU7PYU8ttr8a8d2Qud43TPT5w+/QPrtv6uGBFvEb2PVmqJHETUDQE2tBQNdxn+oib8N1ZwQaQKBgEK6SV9VotLKUgHf0wyolhzy79Da3d9y7EtyORu6rmJeL3b37ShLt6ejF++GSj+i5tu0d9sNl7082VkKaAKtmxhyU4OOzO9FbV7VpI2vgMYa2Gxg8nPAVwxe8Icg9p4kT/xbkqAxEAnl6lgn8d03fNfjfuKKOk+Ji9AOif2zBt1ZAoGBAK1ukSeqKWYgD+d/rk6KZXJiWytv+pZH9t0DdtPJrwFT420FVeKBRUlyTef+5h4E9ZSQNylGsqF1L9nubvqGJj21bk739Z3bN4UbtrBHzhns+5AQ28j+QBC0c735YLoOhe1coXSwIuL/0yqq2A9MrlRG/coVm7rHVns0rEgXV8X/
      alipayPublicKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA1ZQLMVM/1yiCIDcZvAOyoDAuAvpttW8IJhEScXpabV9CZknMdJUGT6DJH9hqwx8Tv6aw8nvksRSBlTCyHh1rcFmqTRvuLE81nDCVCYzk1CR05EZ0UyIL0QqdPwMG3eEpoNGPxVDLJbLMlO6gu33t3cXvPUO2wiVBPh2Zxw+tYjKEoQIhWB82f2JY8X52sSowX6zENmTYs6+5H9qDrRlmgGb059a3/LHBZN6ZcYc4T3GCsTXdQz6r/QRBMBJrsMBFtJLllhAqYheK1sjWHmQHvPSMV7zPECSx3QE8bSgiMwLAcNYTsUFXLXEG9j9QiFqLOKO+Ul76yP2ZtZ4iN4eUhQIDAQAB
      bizToken: ********************************
      principalTag: 4d6cf8e3d55045548afc6a239414efad
      conversionType: 215


md5:
  phone:
    decode:
      url: http://************:8784

# 丰巢配置
fc:
  appKey: BwmYydQm6r
  appSecret: J8d1V7gmp42aqqCeYBjUuTDZFAhqbxfm
  accountId: 101023
  baseUrl: http://fcems-sit2.fcbox.com/openapi
